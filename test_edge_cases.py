#!/usr/bin/env python3
"""
Test edge cases that might cause zoom preservation issues "after a while".
This tests scenarios like fit view button, extreme zoom levels, and rapid interactions.
"""

import sys
import time
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from minimap_viewer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_edge_cases():
    """Test edge cases that might break zoom preservation."""
    app = QApplication(sys.argv)
    
    # Create minimap viewer
    viewer = MinimapViewer()
    viewer.show()
    
    def log_state(step_name):
        current_floor = viewer.get_current_floor()
        current_zoom = viewer.graphics_view.zoom_factor
        print(f"{step_name}: Floor {current_floor}, Zoom: {current_zoom:.6f}")
        return current_zoom
    
    def run_edge_case_test():
        print("=== Edge Case Test for Zoom Preservation ===")
        
        step = 0
        saved_zooms = {}
        
        def next_step():
            nonlocal step
            step += 1
            
            if step == 1:
                log_state("Initial state")
                # Set zoom on floor 7
                viewer.graphics_view.zoom_to_factor(1.5)
                saved_zooms[7] = log_state("Set 1.5x zoom on floor 7")
                QTimer.singleShot(200, next_step)
                
            elif step == 2:
                # Switch to floor 6 and set zoom
                viewer.set_floor(6)
                log_state("Switch to floor 6")
                viewer.graphics_view.zoom_to_factor(2.0)
                saved_zooms[6] = log_state("Set 2.0x zoom on floor 6")
                QTimer.singleShot(200, next_step)
                
            elif step == 3:
                # Test FIT VIEW button - this might reset zoom!
                print("\n--- Testing FIT VIEW button (potential issue) ---")
                viewer.fit_view()
                new_zoom = log_state("After FIT VIEW on floor 6")
                if abs(new_zoom - saved_zooms[6]) > 0.01:
                    print(f"⚠️  FIT VIEW changed zoom from {saved_zooms[6]:.6f} to {new_zoom:.6f}")
                    saved_zooms[6] = new_zoom  # Update expectation
                QTimer.singleShot(200, next_step)
                
            elif step == 4:
                # Switch to floor 7 - should still be 1.5x
                viewer.set_floor(7)
                restored_zoom = log_state("Back to floor 7")
                if abs(restored_zoom - saved_zooms[7]) > 0.01:
                    print(f"❌ Floor 7 zoom BROKEN! Expected {saved_zooms[7]:.6f}, got {restored_zoom:.6f}")
                else:
                    print(f"✅ Floor 7 zoom preserved correctly")
                QTimer.singleShot(200, next_step)
                
            elif step == 5:
                # Test extreme zoom levels
                print("\n--- Testing extreme zoom levels ---")
                viewer.graphics_view.zoom_to_factor(0.1)  # Minimum zoom
                saved_zooms[7] = log_state("Extreme zoom 0.1x on floor 7")
                QTimer.singleShot(200, next_step)
                
            elif step == 6:
                viewer.set_floor(8)
                log_state("Switch to floor 8")
                viewer.graphics_view.zoom_to_factor(20.0)  # Maximum zoom
                saved_zooms[8] = log_state("Extreme zoom 20.0x on floor 8")
                QTimer.singleShot(200, next_step)
                
            elif step == 7:
                # Switch back to floor 7 - should be 0.1x
                viewer.set_floor(7)
                restored_zoom = log_state("Back to floor 7 (should be 0.1x)")
                if abs(restored_zoom - saved_zooms[7]) > 0.01:
                    print(f"❌ Extreme zoom preservation FAILED!")
                else:
                    print(f"✅ Extreme zoom preserved correctly")
                QTimer.singleShot(200, next_step)
                
            elif step == 8:
                # Test rapid zoom changes
                print("\n--- Testing rapid zoom changes ---")
                for i in range(10):
                    viewer.graphics_view.zoom(True)  # Rapid zoom in
                final_zoom = log_state("After rapid zoom changes on floor 7")
                saved_zooms[7] = final_zoom
                QTimer.singleShot(200, next_step)
                
            elif step == 9:
                # Test zoom slider extreme values
                print("\n--- Testing zoom slider edge cases ---")
                viewer.set_floor(5)
                log_state("Switch to floor 5")
                viewer.zoom_slider.setValue(10)  # Minimum slider value (0.1x)
                saved_zooms[5] = log_state("Zoom slider minimum on floor 5")
                QTimer.singleShot(200, next_step)
                
            elif step == 10:
                viewer.zoom_slider.setValue(2000)  # Maximum slider value (20.0x)
                saved_zooms[5] = log_state("Zoom slider maximum on floor 5")
                QTimer.singleShot(200, next_step)
                
            elif step == 11:
                # Test if zoom preservation still works after slider extremes
                viewer.set_floor(7)
                restored_zoom = log_state("Back to floor 7 after slider extremes")
                if abs(restored_zoom - saved_zooms[7]) > 0.01:
                    print(f"❌ Zoom preservation BROKEN after slider extremes!")
                else:
                    print(f"✅ Zoom preservation still works after slider extremes")
                QTimer.singleShot(200, next_step)
                
            elif step == 12:
                # Test very rapid floor switching (stress test)
                print("\n--- Stress test: Very rapid floor switching ---")
                floors = [5, 7, 8, 5, 7, 8, 5, 7, 8] * 3  # 27 rapid switches
                
                def rapid_switch(index=0):
                    if index < len(floors):
                        viewer.set_floor(floors[index])
                        if index % 9 == 0:  # Log every 9th switch
                            log_state(f"Rapid switch {index+1}/{len(floors)}")
                        QTimer.singleShot(50, lambda: rapid_switch(index + 1))
                    else:
                        QTimer.singleShot(200, next_step)
                
                rapid_switch()
                
            elif step == 13:
                # Final verification after stress test
                print("\n--- Final verification after stress test ---")
                
                # Check floor 7
                viewer.set_floor(7)
                zoom_7 = log_state("Final check floor 7")
                if abs(zoom_7 - saved_zooms[7]) > 0.01:
                    print(f"❌ Floor 7 FAILED after stress test! Expected {saved_zooms[7]:.6f}")
                else:
                    print(f"✅ Floor 7 survived stress test")
                
                QTimer.singleShot(200, next_step)
                
            elif step == 14:
                # Check floor 5
                viewer.set_floor(5)
                zoom_5 = log_state("Final check floor 5")
                if abs(zoom_5 - saved_zooms[5]) > 0.01:
                    print(f"❌ Floor 5 FAILED after stress test! Expected {saved_zooms[5]:.6f}")
                else:
                    print(f"✅ Floor 5 survived stress test")
                
                QTimer.singleShot(200, next_step)
                
            elif step == 15:
                # Check floor 8
                viewer.set_floor(8)
                zoom_8 = log_state("Final check floor 8")
                if abs(zoom_8 - saved_zooms[8]) > 0.01:
                    print(f"❌ Floor 8 FAILED after stress test! Expected {saved_zooms[8]:.6f}")
                else:
                    print(f"✅ Floor 8 survived stress test")
                
                QTimer.singleShot(500, next_step)
                
            else:
                print("\n=== Edge Case Test Complete ===")
                print("Summary of saved zoom levels:")
                positions = viewer.graphics_view.floor_camera_positions
                for floor_id in sorted(positions.keys()):
                    data = positions[floor_id]
                    expected = saved_zooms.get(floor_id, "N/A")
                    status = "✅" if abs(data['zoom'] - expected) < 0.01 else "❌"
                    print(f"  {status} Floor {floor_id}: {data['zoom']:.6f} (expected: {expected})")
                
                print("\nIf you see any ❌ above, there's still a zoom preservation issue.")
                print("If you see only ✅, the zoom preservation is working correctly.")
                
                QTimer.singleShot(2000, app.quit)
        
        next_step()
    
    # Start the test
    QTimer.singleShot(1000, run_edge_case_test)
    
    # Run the application
    app.exec()

if __name__ == "__main__":
    test_edge_cases()
