#!/usr/bin/env python3
"""
Test realistic usage patterns to reproduce the "after a while" zoom issue.
This simulates more realistic user behavior with mixed interactions.
"""

import sys
import time
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from minimap_viewer import <PERSON>map<PERSON>iewer

def test_realistic_usage():
    """Test realistic usage patterns."""
    app = QApplication(sys.argv)
    
    # Create minimap viewer
    viewer = MinimapViewer()
    viewer.show()
    
    # Track zoom expectations
    expected_zooms = {}
    
    def log_and_verify(step_name, expected_zoom=None):
        current_floor = viewer.get_current_floor()
        current_zoom = viewer.graphics_view.zoom_factor
        
        if expected_zoom is not None:
            if abs(current_zoom - expected_zoom) > 0.01:
                print(f"❌ {step_name}: Floor {current_floor}, Expected: {expected_zoom:.4f}, Got: {current_zoom:.4f}")
                return False
            else:
                print(f"✅ {step_name}: Floor {current_floor}, Zoom: {current_zoom:.4f}")
                return True
        else:
            print(f"📝 {step_name}: Floor {current_floor}, Zoom: {current_zoom:.4f}")
            return True
    
    def run_realistic_test():
        print("=== Realistic Usage Pattern Test ===")
        
        step = 0
        
        def next_step():
            nonlocal step
            step += 1
            
            if step == 1:
                log_and_verify("Start on floor 7")
                # User explores a bit, then zooms in on floor 7
                viewer.graphics_view.zoom_to_factor(1.8)
                expected_zooms[7] = 1.8
                log_and_verify("Zoom to 1.8x on floor 7", 1.8)
                QTimer.singleShot(300, next_step)
                
            elif step == 2:
                # User goes to floor 6 to check something
                viewer.set_floor(6)
                log_and_verify("Switch to floor 6")
                QTimer.singleShot(300, next_step)
                
            elif step == 3:
                # User zooms in more on floor 6
                viewer.graphics_view.zoom_to_factor(2.2)
                expected_zooms[6] = 2.2
                log_and_verify("Zoom to 2.2x on floor 6", 2.2)
                QTimer.singleShot(300, next_step)
                
            elif step == 4:
                # User goes back to floor 7 - should restore 1.8x
                viewer.set_floor(7)
                log_and_verify("Back to floor 7", expected_zooms[7])
                QTimer.singleShot(300, next_step)
                
            elif step == 5:
                # User explores floor 8
                viewer.set_floor(8)
                log_and_verify("Switch to floor 8")
                QTimer.singleShot(300, next_step)
                
            elif step == 6:
                # User sets zoom on floor 8
                viewer.graphics_view.zoom_to_factor(1.5)
                expected_zooms[8] = 1.5
                log_and_verify("Zoom to 1.5x on floor 8", 1.5)
                QTimer.singleShot(300, next_step)
                
            elif step == 7:
                # User goes to floor 5 (new floor)
                viewer.set_floor(5)
                log_and_verify("Switch to floor 5")
                QTimer.singleShot(300, next_step)
                
            elif step == 8:
                # User uses zoom slider on floor 5
                viewer.zoom_slider.setValue(250)  # 2.5x
                expected_zooms[5] = 2.5
                log_and_verify("Zoom slider to 2.5x on floor 5", 2.5)
                QTimer.singleShot(300, next_step)
                
            elif step == 9:
                # User goes back to floor 6 - should restore 2.2x
                viewer.set_floor(6)
                log_and_verify("Back to floor 6", expected_zooms[6])
                QTimer.singleShot(300, next_step)
                
            elif step == 10:
                # User goes to floor 4 (new floor)
                viewer.set_floor(4)
                log_and_verify("Switch to floor 4")
                QTimer.singleShot(300, next_step)
                
            elif step == 11:
                # User uses mouse wheel zoom on floor 4
                # Simulate multiple wheel events
                for i in range(5):
                    viewer.graphics_view.zoom(True)  # Zoom in 5 times
                current_zoom = viewer.graphics_view.zoom_factor
                expected_zooms[4] = current_zoom
                log_and_verify("Mouse wheel zoom on floor 4", current_zoom)
                QTimer.singleShot(300, next_step)
                
            elif step == 12:
                # User goes back to floor 7 - should restore 1.8x
                viewer.set_floor(7)
                log_and_verify("Back to floor 7 again", expected_zooms[7])
                QTimer.singleShot(300, next_step)
                
            elif step == 13:
                # User goes back to floor 8 - should restore 1.5x
                viewer.set_floor(8)
                log_and_verify("Back to floor 8", expected_zooms[8])
                QTimer.singleShot(300, next_step)
                
            elif step == 14:
                # User goes back to floor 5 - should restore 2.5x
                viewer.set_floor(5)
                log_and_verify("Back to floor 5", expected_zooms[5])
                QTimer.singleShot(300, next_step)
                
            elif step == 15:
                # User goes back to floor 4 - should restore wheel zoom
                viewer.set_floor(4)
                log_and_verify("Back to floor 4", expected_zooms[4])
                QTimer.singleShot(300, next_step)
                
            elif step == 16:
                # Final verification - rapid switching
                print("\n--- Final verification with rapid switching ---")
                floors_to_check = [7, 6, 8, 5, 4, 7, 6]
                
                def verify_floor(floor_index=0):
                    if floor_index < len(floors_to_check):
                        floor = floors_to_check[floor_index]
                        viewer.set_floor(floor)
                        expected = expected_zooms.get(floor)
                        if expected:
                            success = log_and_verify(f"Verify floor {floor}", expected)
                            if not success:
                                print(f"💥 ZOOM PRESERVATION FAILED on floor {floor}!")
                        else:
                            log_and_verify(f"Check floor {floor} (no expectation)")
                        QTimer.singleShot(200, lambda: verify_floor(floor_index + 1))
                    else:
                        QTimer.singleShot(500, next_step)
                
                verify_floor()
                
            else:
                print("\n=== Realistic Usage Test Complete ===")
                print("If you see any ❌ above, the zoom preservation is broken.")
                print("If you see only ✅ and 📝, the zoom preservation is working correctly.")
                
                # Print final state
                print("\n--- Final Camera Positions ---")
                positions = viewer.graphics_view.floor_camera_positions
                for floor_id in sorted(positions.keys()):
                    data = positions[floor_id]
                    expected = expected_zooms.get(floor_id, "N/A")
                    print(f"Floor {floor_id}: zoom={data['zoom']:.4f} (expected: {expected})")
                
                QTimer.singleShot(2000, app.quit)
        
        next_step()
    
    # Start the test
    QTimer.singleShot(1000, run_realistic_test)
    
    # Run the application
    app.exec()

if __name__ == "__main__":
    test_realistic_usage()
