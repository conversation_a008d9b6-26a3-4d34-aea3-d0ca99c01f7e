#!/usr/bin/env python3
"""
Debug script to catch the zoom preservation issue that occurs "after a while".
This will simulate extended usage and monitor zoom changes closely.
"""

import sys
import time
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from minimap_viewer import <PERSON><PERSON>p<PERSON>iewer

def debug_zoom_issue():
    """Debug the zoom issue with extended testing."""
    app = QApplication(sys.argv)
    
    # Create minimap viewer
    viewer = MinimapViewer()
    viewer.show()
    
    # Track zoom changes
    zoom_history = []
    floor_history = []
    
    def log_state(step_name):
        current_floor = viewer.get_current_floor()
        current_zoom = viewer.graphics_view.zoom_factor
        zoom_history.append(current_zoom)
        floor_history.append(current_floor)
        print(f"{step_name}: Floor {current_floor}, Zoom: {current_zoom:.6f}")
        
        # Check for unexpected zoom changes
        if len(zoom_history) > 1:
            prev_zoom = zoom_history[-2]
            prev_floor = floor_history[-2]
            if current_floor == prev_floor and abs(current_zoom - prev_zoom) > 0.001:
                print(f"  ⚠️  UNEXPECTED ZOOM CHANGE on same floor! {prev_zoom:.6f} → {current_zoom:.6f}")
    
    def run_extended_test():
        print("=== Extended Zoom Preservation Debug Test ===")
        
        step = 0
        
        def next_step():
            nonlocal step
            step += 1
            
            if step == 1:
                log_state("Initial")
                # Set a specific zoom on floor 7
                viewer.graphics_view.zoom_to_factor(1.5)
                log_state("After zoom to 1.5x on floor 7")
                QTimer.singleShot(200, next_step)
                
            elif step == 2:
                # Switch to floor 6
                viewer.set_floor(6)
                log_state("Switched to floor 6")
                QTimer.singleShot(200, next_step)
                
            elif step == 3:
                # Set different zoom on floor 6
                viewer.graphics_view.zoom_to_factor(2.0)
                log_state("After zoom to 2.0x on floor 6")
                QTimer.singleShot(200, next_step)
                
            elif step == 4:
                # Switch to floor 5
                viewer.set_floor(5)
                log_state("Switched to floor 5")
                QTimer.singleShot(200, next_step)
                
            elif step == 5:
                # Set zoom on floor 5
                viewer.graphics_view.zoom_to_factor(2.5)
                log_state("After zoom to 2.5x on floor 5")
                QTimer.singleShot(200, next_step)
                
            elif step == 6:
                # Rapid floor switching to stress test
                print("\n--- Starting rapid floor switching stress test ---")
                floors_to_test = [7, 6, 5, 4, 3, 4, 5, 6, 7, 8, 9, 8, 7, 6, 5]
                
                def rapid_switch(floor_index=0):
                    if floor_index < len(floors_to_test):
                        floor = floors_to_test[floor_index]
                        viewer.set_floor(floor)
                        log_state(f"Rapid switch {floor_index+1}/15 to floor {floor}")
                        QTimer.singleShot(100, lambda: rapid_switch(floor_index + 1))
                    else:
                        QTimer.singleShot(200, next_step)
                
                rapid_switch()
                
            elif step == 7:
                print("\n--- Verifying zoom preservation after stress test ---")
                
                # Check floor 7 (should be 1.5x)
                viewer.set_floor(7)
                log_state("Floor 7 verification")
                expected_7 = 1.5
                actual_7 = viewer.graphics_view.zoom_factor
                if abs(actual_7 - expected_7) > 0.01:
                    print(f"  ❌ Floor 7 zoom FAILED: expected {expected_7}, got {actual_7}")
                else:
                    print(f"  ✅ Floor 7 zoom OK")
                
                QTimer.singleShot(200, next_step)
                
            elif step == 8:
                # Check floor 6 (should be 2.0x)
                viewer.set_floor(6)
                log_state("Floor 6 verification")
                expected_6 = 2.0
                actual_6 = viewer.graphics_view.zoom_factor
                if abs(actual_6 - expected_6) > 0.01:
                    print(f"  ❌ Floor 6 zoom FAILED: expected {expected_6}, got {actual_6}")
                else:
                    print(f"  ✅ Floor 6 zoom OK")
                
                QTimer.singleShot(200, next_step)
                
            elif step == 9:
                # Check floor 5 (should be 2.5x)
                viewer.set_floor(5)
                log_state("Floor 5 verification")
                expected_5 = 2.5
                actual_5 = viewer.graphics_view.zoom_factor
                if abs(actual_5 - expected_5) > 0.01:
                    print(f"  ❌ Floor 5 zoom FAILED: expected {expected_5}, got {actual_5}")
                else:
                    print(f"  ✅ Floor 5 zoom OK")
                
                QTimer.singleShot(200, next_step)
                
            elif step == 10:
                print("\n--- Testing zoom slider interaction ---")
                # Test if zoom slider causes issues
                viewer.zoom_slider.setValue(300)  # 3.0x zoom
                log_state("After zoom slider to 3.0x")
                QTimer.singleShot(200, next_step)
                
            elif step == 11:
                # Switch floors after slider change
                viewer.set_floor(4)
                log_state("Switched to floor 4 after slider")
                QTimer.singleShot(200, next_step)
                
            elif step == 12:
                # Switch back
                viewer.set_floor(5)
                log_state("Back to floor 5 after slider test")
                QTimer.singleShot(200, next_step)
                
            else:
                print("\n=== Debug Test Complete ===")
                print("Check the log above for any unexpected zoom changes.")
                
                # Print camera positions for debugging
                print("\n--- Camera Position Debug Info ---")
                positions = viewer.graphics_view.floor_camera_positions
                for floor_id, data in positions.items():
                    print(f"Floor {floor_id}: zoom={data['zoom']:.6f}, center=({data['center'].x():.2f}, {data['center'].y():.2f})")
                
                QTimer.singleShot(2000, app.quit)
        
        next_step()
    
    # Start the test
    QTimer.singleShot(1000, run_extended_test)
    
    # Run the application
    app.exec()

if __name__ == "__main__":
    debug_zoom_issue()
